from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import JobListing, JobSearch
from .serializers import JobListingSerializer, JobSearchSerializer
from .tasks import scrape_jobs
from jobscrarify.celery import app as celery_app
from celery.result import AsyncResult
from django.shortcuts import get_object_or_404

# Create your views here.

class StartSearchView(APIView):
    def post(self, request):
        query = request.data.get('query')
        location = request.data.get('location', '')

        if not query:
            return Response({"error": "query is required"}, status=status.HTTP_400_BAD_REQUEST)
        search = JobSearch.objects.create(query=query, location=location)
        task = scrape_jobs.delay(search.id)
        search.task_id = task.id
        search.save(update_fields=['task_id'])
        serializer = JobSearchSerializer(search)
        return Response(serializer.data, status=status.HTTP_202_ACCEPTED)
    
class SearchStatusView(APIView):
    def get(self, request, pk):
        search = get_object_or_404(JobSearch, pk=pk)
        if not search.task_id:
            return Response({"status": "not started"})
        async_res = AsyncResult(search.task_id, app=celery_app)
        return Response({"status": async_res.status, "task_id": search.task_id})
    
class SearchResultsView(APIView):
    def get(self, request, pk):
        search = get_object_or_404(JobSearch, pk=pk)
        jobs = search.jobs.all().order_by('-created_at')
        serializer = JobListingSerializer(jobs, many=True)
        return Response({"search": search.id, "query": search.query, "results": serializer.data})
